RELEASE NOTES – SEMANA 27
Data: 03/07/2025

:wrench: Platform

:computer: Solicitações Operacionais - OPERAÇÕES

•[29923](https://redmine.platform.brobot.com.br/issues/29923) - SISTEMA LEGADO OFFLINE
•[29861](https://redmine.platform.brobot.com.br/issues/29861) - Email @brobotbr não está funcionando
•[30225](https://redmine.platform.brobot.com.br/issues/30225) - Ativação GIT Cidadão para todas as placas de PR

:white_check_mark: Alteração de nome de contas concluídas:

•[30302](https://redmine.platform.brobot.com.br/issues/30302) - Alteração de nome dos clientes Cocal (ID: 1287) e Cocal Cereais - SNE (ID: 1680)

:white_check_mark: Ativações SNE concluídas:

•[30291](https://redmine.platform.brobot.com.br/issues/30291) - Ativar SNE da conta TRAIL (ID: 1111)
•[29999](https://redmine.platform.brobot.com.br/issues/29999) - Ativar SNE da conta Apresentacao SANCETUR (ID: 1827)
•[29938](https://redmine.platform.brobot.com.br/issues/29938) - Ativar SNE da conta LIDER LOCADORA - SNE (ID: 1824)
•[29935](https://redmine.platform.brobot.com.br/issues/29935) - Ativar SNE da conta UNICARGA - SNE (ID: 1825)
•[29906](https://redmine.platform.brobot.com.br/issues/29906) - Ativar SNE da conta H&H - SNE (ID: 1823)
•[29697](https://redmine.platform.brobot.com.br/issues/29697) - Ativar SNE da conta Grupo Scheffer (ID: 1822)

:bar_chart: Solicitações de relatórios:

•[29862](https://redmine.platform.brobot.com.br/issues/29862) - Gerar relatório de erros das spiders e atividades relacionadas pacote 2025 27(sexta feira)
•[29952](https://redmine.platform.brobot.com.br/issues/29952) - Relatórios de Consultas Avulsas e Boletos - Fechamento 06/2025 - Sistema Legado
•[29953](https://redmine.platform.brobot.com.br/issues/29953) - Fechamento 06/2025 - Platform

---

:tools: Melhorias e correções:

•[29900](https://redmine.platform.brobot.com.br/issues/29900) - Tela de agendamento mostra o agendamento de todos os clientes ao mesmo tempo
•[29882](https://redmine.platform.brobot.com.br/issues/29882) - Tratar erro Erro na leitura do input_payload [undefined method `each' for "Arquivo não disponivel no momento.":String]
•[28681](https://redmine.platform.brobot.com.br/issues/28681) - As multas da ANTT registradas por radar (SERPRO) não estão sendo devidamente entregues ao cliente.
•[28030](https://redmine.platform.brobot.com.br/issues/28030) - ATIVAR CERTIFICADO DIGITAL PARA TODOS OS ROBÔS GOV.BR
•[29607](https://redmine.platform.brobot.com.br/issues/29607) - Nota Fiscal de Serviço não capturada - Diageo (410)


---

:warning: Caçamultas Embarcador:

•[28788](https://redmine.platform.brobot.com.br/issues/28788) - Documentação API - Embarcador
•[29293](https://redmine.platform.brobot.com.br/issues/29293) - FG EXPRESS (ID: 1693) - DNIT embarcador processo travado

---

:spider: Spiders

•[30287](https://redmine.platform.brobot.com.br/issues/30287) - autos_detran_rs - user_error
•[29592](https://redmine.platform.brobot.com.br/issues/29592) - autos_sefaz_sp - Alteração do proxy
•[29393](https://redmine.platform.brobot.com.br/issues/29393) - MONITORAMENTO DE PORTAIS
•[27524](https://redmine.platform.brobot.com.br/issues/27524) - autos_der_sp - Tratar WRONG_CREDENTIALS
•[27515](https://redmine.platform.brobot.com.br/issues/27515) - autos_prefeitura_sp - Sem retorno para placas com muitas multas
•[26200](https://redmine.platform.brobot.com.br/issues/26200) - autos_detran_rs - Mudança para GOV.BR
•[25171](https://redmine.platform.brobot.com.br/issues/25171) - DETRAN MG não captura boleto - Apisul - Transportes Casarino
•[2666](https://redmine.platform.brobot.com.br/issues/2666) - Tratar mudanças no portal DER-PR (GitCidadão)

---

:toolbox: Atividades de operacionais - DEVS:

•[29863](https://redmine.platform.brobot.com.br/issues/29863) - Rotina monitoramento aplicação e infraestrutura - semana 26 e 27
•[29584](https://redmine.platform.brobot.com.br/issues/29584) - MAPEAR INFORMAÇÔES CAPTURADA POR ÒRGÂOS

